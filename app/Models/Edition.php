<?php declare(strict_types=1);

namespace App\Models;

use App\DataTransferObjects\BookDto;
use App\Http\Resources\LanguageResource;
use App\Services\BookPopularityService;
use App\Services\SanitizerService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

/**
 * App\Models\Edition
 *
 * @property int $id
 * @property string|null $title
 * @property int $book_id
 * @property string|null $isbn_13
 * @property string|null $google_id
 * @property string|null $open_library_id
 * @property string|null $description
 * @property string|null $maturity_rating
 * @property int|null $number_of_pages
 * @property string|null $subtitle
 * @property string|null $publish_date
 * @property int|null $publish_year
 * @property int|null $publisher_id
 * @property int|null $language_id
 * @property string|null $cover
 * @property string|null $binding
 * @property string|null $edition
 * @property string|null $dimensions
 * @property float|null $msrp
 * @property float|null $google_rating
 * @property int|null $google_ratings_count
 * @property float|null $open_library_rating
 * @property int|null $open_library_ratings_count
 * @property int|null $open_library_reading_logs_count
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Carbon|null $deleted_at
 */
final class Edition extends Model
{
    use SoftDeletes;

    public const ISBNDB_COVERS_BASE_URL = 'https://images.isbndb.com/covers';
    public const GOOGLE_BOOKS_COVERS_URL = 'https://books.google.com/books/publisher/content?printsec=frontcover&img=1&zoom=5&id=';

    protected $casts = [
        'msrp' => 'float',
        'google_rating' => 'float',
        'open_library_rating' => 'float',
    ];
    private SanitizerService $sanitizerService;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        $this->sanitizerService = new SanitizerService();
    }

    public function identifiers(): HasMany
    {
        return $this->hasMany(EditionIdentifier::class)->orderByRaw('
            CASE WHEN type = "' . EditionIdentifier::ISBN_13 . '" THEN 1
            WHEN type = "' . EditionIdentifier::ISBN_10 . '" THEN 2
            WHEN type = "' . EditionIdentifier::OCLC . '" THEN 3
            WHEN type = "' . EditionIdentifier::LCCN . '" THEN 4
            ELSE 5 END
        ');
    }

    public function book(): BelongsTo
    {
        return $this->belongsTo(Book::class)->with([
            'authors',
            'subjects',
        ]);
    }

    public function publisher(): BelongsTo
    {
        return $this->belongsTo(Publisher::class);
    }

    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class);
    }

    public function descriptions(): HasMany
    {
        return $this->hasMany(EditionDescription::class);
    }

    public function getCoverUrl(): ?string
    {
        if (!empty($this->cover)) {
            return self::ISBNDB_COVERS_BASE_URL . $this->cover;
        }

        if (!empty($this->google_id)) {
            return self::GOOGLE_BOOKS_COVERS_URL . $this->google_id;
        }

        return null;
    }

    public function toDTO(): BookDto
    {
        $book = new BookDto();
        $book->uuid = $this->book->uuid;
        $book->title = $this->title;
        $book->subtitle = $this->subtitle;
        $book->description = $this->description;
        $book->maturityRating = $this->maturity_rating;
        $book->numberOfPages = $this->number_of_pages;
        $book->isFree = $this->google_id !== null;
        $book->cover = $this->getCoverUrl();

        if (!empty($this->isbn_13)) {
            $book->identifiers[EditionIdentifier::ISBN_13] = $this->isbn_13;
        }
        $identifiers = $this->identifiers()->displayed()->get();
        foreach ($identifiers as $identifier) {
            $book->identifiers[$identifier->type] = $identifier->number;
        }

        $book->urlIdentifier['key'] = $this->google_id;
        $book->urlIdentifier['type'] = $this->google_id !== null ? 'GID' : '';

        $userLanguage = Language::where('code', Auth::user()?->language ?? 'en')->firstOrFail();
        $book->description = $this->descriptions()->where('language_id', $userLanguage->id)->first()?->text;
        if (empty($book->description) && !empty($this->description)) {
            $book->description = $this->sanitizerService->sanitizeDescription($this->description);
        }

        if (!empty($this->language)) {
            $book->languages[] = (new LanguageResource($this->language))->jsonSerialize();
        }

        foreach ($this->book->authors as $author) {
            $book->authors[] = $author->name;
            $book->authorIDs[] = $author->uuid;
        }

        foreach ($this->book->subjects as $subject) {
            $book->subjects[] = $subject->name;
        }

        if (!empty($this->publisher)) {
            $book->publisher = $this->publisher->name;
        }

        $audiobook = $this->book->audiobook();
        if (!empty($audiobook)) {
            $book->audiobookId = $audiobook->uuid;
        }

        if (!empty($this->publish_year)) {
            $book->publishDates[] = $this->publish_year;
        }

        if (Auth::check()) {
            $book->isLiked = $this->book->isLiked();
            $book->isInLists = $this->book->isInLists();
            $book->readStatus = $this->book->readStatuses()
                ->where('user_id', Auth::id())->first()?->bookReadStatusType->name;
        }

        return $book;
    }

    public function toSearchArray(): array
    {
        $book = [];
        $book['uuid'] = $this->book->uuid;
        $book['title'] = $this->title;
        $book['subtitle'] = $this->subtitle;
        $book['isFree'] = $this->google_id !== null;
        $book['cover'] = $this->getCoverUrl();

        $book['identifiers'] = [];
        if (!empty($this->isbn_13)) {
            $book['identifiers'][EditionIdentifier::ISBN_13] = $this->isbn_13;
        }
        $identifiers = $this->identifiers()->displayed()->get();
        foreach ($identifiers as $identifier) {
            $book['identifiers'][$identifier->type] = $identifier->number;
        }

        $book['urlIdentifier']['key'] = $this->google_id;
        $book['urlIdentifier']['type'] = $this->google_id !== null ? 'GID' : '';

        $book['languages'] = [];
        if (!empty($this->language)) {
            $book['languages'][] = (new LanguageResource($this->language))->jsonSerialize();
        }

        $book['authors'] = [];
        foreach ($this->book->authors as $author) {
            $book['authors'][] = $author->name;
        }

        $book['subjects'] = [];
        foreach ($this->book->subjects as $subject) {
            $book['subjects'][] = $subject->name;
        }

        $book['publishDates'] = [];
        if (!empty($this->publish_year)) {
            $book['publishDates'][] = $this->publish_year;
        }

        $book['popularity'] = BookPopularityService::getPopularityScore($this);

        return $book;
    }
}
