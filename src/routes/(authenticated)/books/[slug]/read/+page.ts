import type {PageLoad} from './$types';

/*
 * Pre-rendering should be disabled for the book pages because there are too many of them. In the future we could enable
 * this just for the most popular ones. SSR is also disabled because we only use it for pre-rendering.
 */
export const prerender = false;
export const ssr = false;

export const load: PageLoad = function ({params}) {
    return {
        slug: params.slug,
    };
};
