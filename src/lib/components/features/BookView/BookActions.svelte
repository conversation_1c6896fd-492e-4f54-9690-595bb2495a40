<script lang="ts">
    import ChevronDown from "lucide-svelte/icons/chevron-down";
    import {type ComponentType, onMount} from 'svelte';
    import {goto} from '$app/navigation';
    import {page} from '$app/stores';
    import BookListsApiClient from '$lib/api/BookListsApiClient';
    import BookReadStatusesApiClient from '$lib/api/BookReadStatusesApiClient';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import LikesApiClient from '$lib/api/LikesApiClient';
    import BookClosedSvg from '$lib/components/svg/BookClosedSvg.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import FacebookSvg from '$lib/components/svg/FacebookSvg.svelte';
    import FolderPlusSvg from '$lib/components/svg/FolderPlusSvg.svelte';
    import PinterestSvg from '$lib/components/svg/PinterestSvg.svelte';
    import ShareSvg from '$lib/components/svg/ShareSvg.svelte';
    import StarSvg from '$lib/components/svg/StarSvg.svelte';
    import ThumbsDownOutlineSvg from '$lib/components/svg/ThumbsDownOutlineSvg.svelte';
    import ThumbsDownSvg from '$lib/components/svg/ThumbsDownSvg.svelte';
    import ThumbsUpOutlineSvg from '$lib/components/svg/ThumbsUpOutlineSvg.svelte';
    import ThumbsUpSvg from '$lib/components/svg/ThumbsUpSvg.svelte';
    import XCircle from '$lib/components/svg/XCircle.svelte';
    import XSvg from "$lib/components/svg/XSvg.svelte";
    import XTheCompanySvg from '$lib/components/svg/XTheCompanySvg.svelte';
    import Button from "$lib/components/ui/Button.svelte";
    import Checkbox from '$lib/components/ui/Checkbox.svelte';
    import Dropdown from '$lib/components/ui/Dropdown.svelte';
    import Modal from '$lib/components/ui/Modal.svelte';
    import type Book from '$lib/domain/Book';
    import type BookList from '$lib/domain/BookList';
    import type BookReadStatusType from "$lib/domain/BookReadStatusType";
    import {t} from '$lib/localization/Localization';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
    import ToastNotificationsStore from '$lib/stores/ToastNotificationsStore';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    type ReadStatusOption = {
        value: BookReadStatusType;
        label: string;
        icon: ComponentType;
        bgColor: string;
        textColor: string;
    };

    const readStatusOptions: ReadStatusOption[] = [
         {
            value: 'wantToRead',
            label: $t('bookStatuses.wantToRead'),
            icon: StarSvg,
            bgColor: 'bg-orange-500',
            textColor: 'text-white',
        },
        {
            value: 'currentlyReading',
            label: $t('bookStatuses.currentlyReading'),
            icon: BookOpenSvg,
            bgColor: 'bg-green-500',
            textColor: 'text-white',
        },
        {
            value: 'read',
            label: $t('bookStatuses.finished'),
            icon: BookClosedSvg,
            bgColor: 'bg-green-700',
            textColor: 'text-white',
        },
        {
            value: 'dropped',
            label: $t('bookStatuses.abandoned'),
            icon: XCircle,
            bgColor: 'bg-gray-500',
            textColor: 'text-white',
        },
    ];

    type ShareOption = {
        value: string;
        label: string;
        icon: ComponentType;
        action: () => void;
    };

    const shareOptions: ShareOption[] = [
        {
            value: 'facebook',
            label: 'Facebook',
            icon: FacebookSvg,
            action: () => shareOnFacebook(),
        },
        {
            value: 'pinterest',
            label: 'Pinterest',
            icon: PinterestSvg,
            action: () => shareOnPinterest(),
        },
         {
            value: 'x',
            label: 'X',
            icon: XTheCompanySvg,
            action: () => shareOnX(),
        },
    ];

    let selectedReadStatus = book.readStatus
        ? readStatusOptions.find((option) => option.value === book.readStatus)
        : null;

    let addToBookListModalOpen = false;
    let bookLists: BookList[] = [];
    const bookListsCheckboxes: boolean[] = [];

    async function handleLikeAction(likeState: -1 | 0 | 1 = 1) {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);

            return;
        }

        const previousLikeState = book.isLiked;
        book.isLiked = likeState;

        let apiRoute;
        if (likeState === 1) {
            apiRoute = 'like';
        } else if (likeState === 0) {
            apiRoute = 'removeLike';
        } else {
            apiRoute = 'dislike';
        }

        const likesApiClient = new LikesApiClient();
        const response = await likesApiClient[apiRoute](book.uuid);

        if (!response.ok || !response.data.success) {
            book.isLiked = previousLikeState;
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }

    async function handleReadStatusChange(option: ReadStatusOption | null) {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);
            return;
        }

        if (option === null) {
            book.readStatus = null;
            selectedReadStatus = null;

            const bookReadStatusesApiClient = new BookReadStatusesApiClient();
            const response = await bookReadStatusesApiClient.removeBookStatus(book.uuid);

            if (!response.ok || !response.data.success) {
                ToastNotificationsStore.push({
                    text: t.get('notifications.genericError', {}, {}),
                    type: 'warning',
                });
            }

            return;
        }

        if (option.value === book.readStatus) {
            return;
        }

        book.readStatus = option.value;
        selectedReadStatus = option;

        const bookReadStatusesApiClient = new BookReadStatusesApiClient();
        const response = await bookReadStatusesApiClient.setBookStatus(book.uuid, option.value);

        if (!response.ok || !response.data.success) {
            selectedReadStatus = book.readStatus
                ? readStatusOptions.find((opt) => opt.value === book.readStatus)
                : null;
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }

    function shareOnX() {
        let query = `text=${encodeURIComponent(book.title)}&url=${encodeURIComponent($page.url.href)}&via=LiberomApp`;
        const hashtags = ['books', 'reading'];
        if (hashtags.length > 0) {
            query += `&hashtags=${hashtags.map((hashtag) => encodeURIComponent(hashtag)).join(',')}`;
        }

        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://x.com/intent/post?${query}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'x');
    }

    function shareOnFacebook() {
        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent($page.url.href)}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'facebook');
    }

    function shareOnPinterest() {
        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://pinterest.com/pin/create/button/?url=${encodeURIComponent($page.url.href)}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(book.uuid, 'pinterest');
    }

    async function openBookListsModal() {
        if (!$AuthenticatedStore) {
            await goto(AppRoutes.login);
        }

        addToBookListModalOpen = true;
    }

    async function loadBookLists() {
        if (!$AuthenticatedStore) {
            return;
        }

        const bookListsApiClient = new BookListsApiClient();
        const response = await bookListsApiClient.index();
        if (!response.ok) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });

            return;
        }

        bookLists = response.data.lists;
        bookLists.forEach((bookList) => {
            bookListsCheckboxes[bookList.id] = book.isInLists[bookList.id];
        });
    }

    async function changeBookListStatus(event) {
        book.isInLists[event.target.id] = event.target.checked;
        const booksApiClient = new BooksApiClient();

        if (event.target.checked) {
            const response = await booksApiClient.addToList(book.uuid, event.target.id);
            if (!response.ok || !response.data.success) {
                ToastNotificationsStore.push({
                    text: t.get('notifications.genericError', {}, {}),
                    type: 'warning',
                });
            }

            return;
        }

        const response = await booksApiClient.removeFromList(book.uuid, event.target.id);
        if (!response.ok || !response.data.success) {
            ToastNotificationsStore.push({
                text: t.get('notifications.genericError', {}, {}),
                type: 'warning',
            });
        }
    }

    onMount(() => {
        // todo: move this to the call that loads the page
        // currently it runs twice because BookActions is used twice in BookView
        loadBookLists();
    });
</script>

<div class="mt-6 space-y-5">
    <div class="flex items-center justify-between">
        <Dropdown
            options={shareOptions}
            buttonClass="inline-flex items-center rounded-xl bg-gray-600 p-1.5 text-center align-middle text-sm font-medium text-white hover:bg-gray-700 focus:outline-none  dark:bg-gray-600 dark:hover:bg-gray-700"
            dropdownClass="absolute left-0 z-[9999] mt-2 w-48 origin-top-left rounded-md bg-white shadow-xl ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700 dark:ring-gray-600"
        >
            <svelte:fragment slot="button">
                <ShareSvg svgClass="w-5 h-5" />
            </svelte:fragment>
        </Dropdown>
        <div class="flex rounded-xl overflow-hidden min-w-[80px]">
            <button
                type="button"
                class="flex items-center justify-center px-3 py-1.5 text-white bg-blue-700 hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none transition-colors flex-1"
                title={$t('book.like')}
                on:click={() => (book.isLiked === 1 ? handleLikeAction(0) : handleLikeAction(1))}
            >
                <svelte:component
                    this={book.isLiked === 1 ? ThumbsUpSvg : ThumbsUpOutlineSvg}
                    svgClass="w-5 h-5"
                />
            </button>
            <div class="w-px bg-blue-500 dark:bg-blue-400 self-stretch"></div>
            <button
                type="button"
                class="flex items-center justify-center px-3 py-1.5 text-white bg-blue-700 hover:bg-blue-800 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none transition-colors flex-1"
                title={$t('book.dislike')}
                on:click={() => (book.isLiked === -1 ? handleLikeAction(0) : handleLikeAction(-1))}
            >
                <svelte:component
                    this={book.isLiked === -1 ? ThumbsDownSvg : ThumbsDownOutlineSvg}
                    svgClass="w-5 h-5"
                />
            </button>
        </div>
    </div>
    <Dropdown
        options={readStatusOptions}
        selectedOption={selectedReadStatus}
        placeholder={$t('bookStatuses.setReadingStatus')}
        showClearOption={true}
        clearOptionLabel="Clear status"
        clearOptionIcon={XSvg}
        buttonClass="inline-flex w-full items-center justify-between rounded-md px-4 py-2 text-sm font-medium shadow-sm focus:outline-none {selectedReadStatus
            ? `${selectedReadStatus.bgColor} ${selectedReadStatus.textColor} hover:opacity-90`
            : 'bg-primary-700 text-white hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700'}"
        on:select={(event) => handleReadStatusChange(event.detail)}
        on:clear={() => handleReadStatusChange(null)}
    >
        <svelte:fragment slot="button" let:placeholder let:selectedOption>
            <div class="flex items-center space-x-2">
                {#if selectedOption}
                    <svelte:component this={selectedOption.icon} svgClass="w-4 h-4 text-white"/>
                    <span>{selectedOption.label}</span>
                {:else}
                    <span>{placeholder}</span>
                {/if}
            </div>
            <ChevronDown class="ml-2 h-5 w-5 -mr-1"/>
        </svelte:fragment>
    </Dropdown>
    <div>
        <Button title={$t('book.addList')} callback={openBookListsModal} icon={FolderPlusSvg} />
        <Modal bind:open={addToBookListModalOpen}>
            <div class="mb-4 flex justify-between rounded-t sm:mb-5">
                <div class="text-lg text-gray-900 dark:text-white md:text-xl">
                    <h3 class="font-semibold">{$t('book.addList')}</h3>
                </div>
            </div>
            <div class="space-y-4">
                {#each bookLists as bookList, index (index)}
                    <Checkbox
                        id={bookList.id}
                        text={bookList.name}
                        checked={book.isInLists[bookList.id]}
                        onChange={changeBookListStatus}
                    />
                {/each}
            </div>
        </Modal>
    </div>
</div>
