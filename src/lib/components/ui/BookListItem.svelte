<script lang="ts">
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import GiftSvg from '$lib/components/svg/GiftSvg.svelte';
    import LanguageSvg from '$lib/components/svg/LanguageSvg.svelte';
    import ListBulletSvg from '$lib/components/svg/ListBulletSvg.svelte';
    import TrashSvg from '$lib/components/svg/TrashSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import type Book from '$lib/domain/Book';
    import type SearchBook from '$lib/domain/SearchBook';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book | SearchBook;
    export let deleteCallback: ((bookIdentifier: string) => void) | undefined = undefined;
</script>

<a href={AppRoutes.book(book.uuid)} class="flex flex-col">
    <div
        class="rounded-lg border border-gray-200 bg-gray-50 p-4 shadow hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:bg-gray-600"
    >
        <div class="flex-1 sm:flex">
            <div class="relative mb-3 h-32 w-32 flex-none sm:mb-0">
                <img src={book.cover} class="h-32 w-32 rounded-2xl object-contain" alt="Book cover" />
            </div>
            <div class="flex-auto justify-evenly sm:ml-5">
                <div class="flex items-center justify-between sm:mt-2">
                    <div class="flex items-center">
                        <div class="flex flex-col">
                            <div
                                class="line-clamp-1 w-full flex-none text-lg font-bold leading-none text-gray-700 dark:text-white"
                            >
                                {book.title}
                            </div>
                            <div class="my-1 flex-auto text-gray-500 dark:text-gray-400">
                                <span>{book.authors.join(', ')}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex pt-2 text-sm text-gray-500 dark:text-gray-400">
                    <div class="inline-flex flex-1 items-center">
                        <LanguageSvg svgClass="mr-2 h-5 w-5" />
                        <span>{book.languages.map((language) => language.text).join(', ')}</span>
                    </div>
                </div>
            </div>
            <div class="mr-4 flex w-[190px] flex-shrink-0 flex-col space-y-2 self-center">
                <Button title={$t('book.details')} icon={ListBulletSvg} />
                <Button href={AppRoutes.bookSummary(book.uuid)} title={$t('book.summary')} icon={BookOpenSvg} />
                {#if book.isFree}
                    <Button href={AppRoutes.bookRead(book.urlIdentifier.key)} title={$t('book.read')} icon={GiftSvg} />
                {/if}
            </div>
            {#if deleteCallback !== undefined}
                <div class="mr-4 content-evenly">
                    <button
                        type="button"
                        on:click={(event) => {
                            event.preventDefault();
                            event.stopPropagation();
                            deleteCallback(book.uuid);
                        }}
                    >
                        <TrashSvg />
                    </button>
                </div>
            {/if}
        </div>
    </div>
</a>
