<?php declare(strict_types=1);

namespace App\DataTransferObjects;

use Illuminate\Contracts\Support\Arrayable;

final class BookDto implements Arrayable
{
    public string $uuid;
    public string $title;
    public ?string $cover = null;
    public ?string $titleLong = null;
    public ?string $subtitle = null;
    public ?string $google_id = null;
    public ?string $description = null;
    public ?string $maturityRating = null;
    public ?string $publisher = null;
    public ?string $binding = null;
    public ?string $edition = null;
    public ?string $dimensions = null;
    public ?string $audiobookId = null;
    public ?int $numberOfPages;
    public ?float $msrp = null;
    public bool $isFree = false;
    public array $identifiers = [];
    public array $extraIdentifiers = [];
    public array $urlIdentifier = [
        'key' => '',
        'type' => '',
    ];
    public array $authors = [];
    public array $authorIDs = [];
    public array $subjects = [];
    public array $languages = [];
    public array $publishDates = [];
    public array $translatedTitles = [];
    public int $isLiked = 0;
    public array $isInLists = [];
    public ?string $readStatus = null;

    public function toArray(): array
    {
        return (array)$this;
    }
}
