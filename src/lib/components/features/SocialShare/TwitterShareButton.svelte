<script lang="ts">
    import BooksApiClient from '$lib/api/BooksApiClient';
    import TwitterSvg from '$lib/components/svg/TwitterSvg.svelte';
    import IconButton from '$lib/components/ui/IconButton.svelte';

    export let text: string;
    export let url: string;
    export let hashtags: string[] = [];
    export let bookId: string;

    function share() {
        let query = `text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&via=LiberomApp`;
        if (hashtags.length > 0) {
            query += `&hashtags=${hashtags.map((hashtag) => encodeURIComponent(hashtag)).join(',')}`;
        }

        const popupWidth = 600;
        const popupHeight = 400;
        const popupX = (screen.width - popupWidth) / 2;
        const popupY = (screen.height - popupHeight) / 2;

        window.open(
            `https://twitter.com/intent/tweet?${query}`,
            '_blank',
            `width=${popupWidth},height=${popupHeight},left=${popupX},top=${popupY}`,
        );

        const booksApiClient = new BooksApiClient();
        booksApiClient.share(bookId, 'twitter');
    }
</script>

<IconButton
    icon={TwitterSvg}
    callback={share}
    backgroundColor="sky-500"
    backgroundColorDark="sky-500"
    backgroundColorHover="sky-500"
    backgroundColorHoverDark="sky-500"
/>
