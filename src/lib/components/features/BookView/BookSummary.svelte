<script lang="ts">
    import {error} from '@sveltejs/kit';
    import {PUBLIC_API_URL} from '$env/static/public';
    import BooksApiClient from '$lib/api/BooksApiClient';
    import Container from '$lib/components/layout/body/Container.svelte';
    import SpeakerWaveSvg from '$lib/components/svg/SpeakerWaveSvg.svelte';
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import Button from '$lib/components/ui/Button.svelte';
    import type Book from '$lib/domain/Book';
    import type BookSummary from '$lib/domain/BookSummary';
    import {t} from '$lib/localization/Localization';
    import AudioPlayerStores from '$lib/stores/AudioPlayerStores';
    import LocaleStore from '$lib/stores/LocaleStore';
    import AppRoutes from '$routes/AppRoutes';
    import StatusCodes from '$routes/StatusCodes';

    export let book: Book;

    const isOpenStore = AudioPlayerStores.isOpen;
    const isPlayingStore = AudioPlayerStores.isPlaying;
    const chapterIndexStore = AudioPlayerStores.chapterIndex;
    const playlistStore = AudioPlayerStores.playlist;

    async function getBookSummary(): Promise<BookSummary> {
        const booksApiClient = new BooksApiClient();
        const response = await booksApiClient.summary(book.uuid);

        if (!response.ok) {
            throw error(StatusCodes.clientError.notFound, 'Not found');
        }

        return response.data;
    }

    function listen() {
        $playlistStore = [
            {
                name: book.title,
                url: `${PUBLIC_API_URL}/books/${book.uuid}/summary-tts/${$LocaleStore}`,
            },
        ];
        $chapterIndexStore = 0;
        $isOpenStore = true;
        $isPlayingStore = true;
    }
</script>

<Container extraClass="space-x-8 lg:flex">
    <div class="mx-auto mb-4 w-1/2 sm:w-1/3 lg:w-1/5 lg:flex-col">
        <img src={book.cover} class="w-full" alt="Book cover" />
    </div>
    <div class="lg:w-4/5 lg:flex-col">
        <h1 class="text-3xl font-bold">{book.title}</h1>
        <div>
            <span>{$t('book.by')}</span>
            {#each book.authors as author, index (index)}
                <a href={AppRoutes.author(book.authorIDs[index])}>{author}</a>
                {#if index < book.authors.length - 1}
                    <span>, </span>
                {/if}
            {/each}
        </div>
        <div class="my-6">
            {#await getBookSummary()}
                <SpinnerSvg svgClass="w-8 h-8 mx-auto" />
            {:then bookSummary}
                <div class="mb-5">
                    <Button title={$t('book.listen')} icon={SpeakerWaveSvg} callback={listen} />
                </div>
                {@html bookSummary.summary}
            {/await}
        </div>
    </div>
</Container>
