<script lang="ts">
    import BooksApiClient from '$lib/api/BooksApiClient';
    import BookActions from '$lib/components/features/BookView/BookActions.svelte';
    import Container from '$lib/components/layout/body/Container.svelte';
    import BookOpenSvg from "$lib/components/svg/BookOpenSvg.svelte";
    import GiftSvg from "$lib/components/svg/GiftSvg.svelte";
    import SpinnerSvg from '$lib/components/svg/SpinnerSvg.svelte';
    import BookListUI from '$lib/components/ui/BookListUI.svelte';
    import Button from "$lib/components/ui/Button.svelte";
    import type Book from '$lib/domain/Book';
    import {t} from '$lib/localization/Localization';
    import BookSearchFiltersService from '$lib/services/BookSearchFiltersService';
    import StringService from '$lib/services/StringService';
    import AppRoutes from '$routes/AppRoutes';

    export let book: Book;

    const identifierLabels = {
        isbn10: 'ISBN 10',
        ISBN10: 'ISBN 10',
        isbn13: 'ISBN 13',
        ISBN13: 'ISBN 13',
    };

    async function getSimilarBooks(bookId: string): Promise<Book[]> {
        const booksApiClient = new BooksApiClient();
        const response = await booksApiClient.similar(bookId);

        if (response.ok) {
            return response.data;
        }

        return [];
    }

    let getSimilarBooksPromise;
    $: uuid = book.uuid; // hack to only refresh getSimilarBooksPromise when uuid changes and not other book properties
    $: getSimilarBooksPromise = getSimilarBooks(uuid);
</script>

<Container extraClass="space-y-4 lg:space-y-0 lg:space-x-8 lg:flex">
    <div class="mx-auto w-1/2 sm:w-1/3 lg:w-1/5 lg:flex-col">
        <img src={book.cover} class="mb-4 w-full" alt="Book cover"/>
        <div class="hidden lg:flex lg:justify-center">
            <div class="inline-block">
                <BookActions book={book}/>
            </div>
        </div>
    </div>
    <div class="lg:w-4/5 lg:flex-col">
        <h1 class="text-3xl font-bold">{book.title}</h1>
        <div>
            <span>{$t('book.by')}</span>
            {#each book.authors as author, index (index)}
                <a href={AppRoutes.author(book.authorIDs[index])}>{author}</a>
                {#if index < book.authors.length - 1}
                    <span>, </span>
                {/if}
            {/each}
        </div>
        <div class="space-x-4 mt-4 text-center lg:text-left">
            {#if book.isFree}
                <Button href={AppRoutes.bookRead(book.urlIdentifier.key)} title={$t('book.read')} icon={GiftSvg}/>
            {/if}
            {#if book.audiobookId}
                <Button href={AppRoutes.audiobook(book.audiobookId)} title={$t('book.audiobook')}/>
            {/if}
            <Button href={AppRoutes.bookSummary(book.uuid)} title={$t('book.summary')} icon={BookOpenSvg}/>
        </div>
        <div class="flex justify-center lg:hidden">
            <div class="inline-block">
                <BookActions book={book}/>
            </div>
        </div>
        <div class="my-6">
            {#if book.description}
                {StringService.stripTags(book.description)}
            {/if}
        </div>
        <div class="space-y-1">
            {#if book.publishDates.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.publishDate')}:</span>
                    <span>{book.publishDates.join(', ')}</span>
                </div>
            {/if}
            {#if book.numberOfPages}
                <div>
                    <span class="font-semibold">{$t('book.numberOfPages')}:</span>
                    <span>{book.numberOfPages}</span>
                </div>
            {/if}
            {#if book.subjects.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.subjects')}:</span>
                    {#each book.subjects as subject, index (index)}
                        <button type="button" on:click={() => BookSearchFiltersService.goToSubjectSearch(subject)}>
                            {subject}
                        </button>
                        {#if index < book.subjects.length - 1}
                            <span>, </span>
                        {/if}
                    {/each}
                </div>
            {/if}
            {#if book.languages.length > 0}
                <div>
                    <span class="font-semibold">{$t('book.languages')}:</span>
                    {#each book.languages as language, index (index)}
                        <button type="button" on:click={() => BookSearchFiltersService.goToLanguageSearch(language)}>
                            {language.text}
                        </button>
                        {#if index < book.languages.length - 1}
                            <span>, </span>
                        {/if}
                    {/each}
                </div>
            {/if}
            {#each Object.entries(book.identifiers) as [identifierType, identifier] (identifierType)}
                <div>
                    <span class="font-semibold">{identifierLabels[identifierType] || identifierType}:</span>
                    <button type="button" on:click={() => BookSearchFiltersService.goToIsbnSearch(identifier)}>
                        {identifier}
                    </button>
                </div>
            {/each}
        </div>
    </div>
</Container>
<Container>
    <h1 class="text-2xl font-bold">{$t('book.similarBooks')}</h1>
    <div class="mt-5 space-y-4">
        {#await getSimilarBooksPromise}
            <SpinnerSvg svgClass="w-8 h-8 mx-auto"/>
        {:then books}
            <BookListUI books={books}/>
        {/await}
    </div>
</Container>
